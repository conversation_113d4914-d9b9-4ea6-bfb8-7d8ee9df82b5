package com.quantchi.knowledge.ai.client;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.quantchi.knowledge.ai.common.ChatUtils;
import com.quantchi.knowledge.ai.common.Constant;
import com.quantchi.knowledge.ai.common.ErrorCode;
import com.quantchi.knowledge.ai.common.RoleEnum;
import com.quantchi.knowledge.ai.entity.Message;
import com.quantchi.knowledge.ai.entity.UserFile;
import com.quantchi.knowledge.ai.entity.chatmodel.ChatErnieRequest;
import com.quantchi.knowledge.ai.entity.chatmodel.DpChatQueryRequest;
import com.quantchi.knowledge.ai.entity.request.ErnieRequest;
import com.quantchi.knowledge.ai.entity.response.ChatResponse;
import com.quantchi.knowledge.ai.entity.response.Choice;
import com.quantchi.knowledge.ai.entity.response.DpChatResponse;
import com.quantchi.knowledge.ai.model.BaseBot;
import com.quantchi.knowledge.ai.model.ContBot;
import com.quantchi.knowledge.center.bean.entity.AgentConfig;
import com.quantchi.knowledge.center.bean.entity.SysChatPreserve;
import com.quantchi.knowledge.center.bean.entity.SysFile;
import com.quantchi.knowledge.center.bean.exception.BusinessException;
import com.quantchi.knowledge.center.component.LocalModelComponent;
import com.quantchi.knowledge.center.service.IAgentConfigService;
import com.quantchi.knowledge.center.service.ISysChatPreserveService;
import com.quantchi.knowledge.center.service.ISysFileService;
import com.quantchi.knowledge.center.service.impl.PatentEsInfoService;
import com.quantchi.knowledge.center.util.HttpServletRequestUtil;
import com.quantchi.knowledge.center.util.RedisUtils;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.DigestUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.util.function.Tuples;

import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import static com.quantchi.knowledge.ai.common.Constant.MAX_CONTENT_LENGTH;
import static com.quantchi.knowledge.ai.common.WenXinUtils.*;
import static com.quantchi.knowledge.center.controller.SysFileController.CHAT_DOC_TYPE;

@Slf4j
public abstract class ChatBotClient implements ContBot<ChatErnieRequest>, BaseBot {

    private static final Map<String, Queue<Message>> ERNIE_MESSAGES_HISTORY_MAP = new ConcurrentHashMap<>();
    private static String URL = Constant.ERNIE_BOT_URL;

    @Setter
    private PatentEsInfoService patentEsInfoService;

    @Setter
    private ISysChatPreserveService sysChatPreserveService;

    @Setter
    private ISysFileService sysFileService;

    @Setter
    private LocalModelComponent localModelComponent;

    @Setter
    private IAgentConfigService agentConfigService;

    protected ChatBotClient() {
    }

    protected ChatBotClient(final String url) {
        URL = url;
    }

    protected abstract String getAccessToken();

    @Override
    public String getURL() {
        return URL;
    }

    @Override
    public String getCustomAccessToken() {
        return getAccessToken();
    }

    public Map<String, Queue<Message>> getMessageHistoryMap() {
        for (final Map.Entry<String, Queue<Message>> entry : ERNIE_MESSAGES_HISTORY_MAP.entrySet()) {
            final Queue<Message> value = entry.getValue();
            while (value.size() > 6) {
                value.remove();
            }
        }
        return ERNIE_MESSAGES_HISTORY_MAP;
    }

    @Override
    public Flux<ChatResponse> chatContOfStreamForPatent(final String content, final String msgUid, final String patentId) {
        final HttpServletResponse response = HttpServletRequestUtil.getResponse();
        response.setHeader("Cache-Control", "no-cache");
        response.setHeader("Connection", "keep-alive");
        response.setHeader("Keep-Alive", "timeout=600");
        response.setHeader("Content-Encoding", "none");
        final Map<String, Queue<Message>> messageHistoryMap = getMessageHistoryMap();
        final Queue<Message> messagesHistory = messageHistoryMap.computeIfAbsent(
                msgUid, k -> new LinkedList<>()
        );
        final long loginId = StpUtil.getLoginIdAsLong();
        return Mono.justOrEmpty(Tuples.of(content, msgUid))
                .filter(tuple -> StringUtils.isNotBlank(tuple.getT1()) && StringUtils.isNotBlank(tuple.getT2()))
                .switchIfEmpty(Mono.error(new BusinessException(ErrorCode.PARAMS_ERROR)))
                .flatMapMany(tuple -> Mono.fromCallable(() -> {  // 将阻塞操作封装在Mono.fromCallable()中
                            final StringBuilder prompt = new StringBuilder(128);
                            if (messagesHistory.isEmpty() && StringUtils.isNotBlank(patentId)) {
                                log.info("=== chatContOfStreamForPatent 接口 === 第一次对话，从数据库获取专利信息，专利id为{}", patentId);
                                // 异步执行阻塞的数据库查询
                                final Map<String, Object> patentBaseInfo = patentEsInfoService.patentBaseInfo(patentId);
                                if (MapUtil.isNotEmpty(patentBaseInfo)) {
                                    prompt.append("专利标题为")
                                            .append((String) patentBaseInfo.get("title"), 0,
                                                    Math.min(1000, ((String) patentBaseInfo.get("title")).length())).append("\n")
                                            .append("专利简介为").append((String) patentBaseInfo.get("abstract"), 0, Math.min(1000, ((String) patentBaseInfo.get("abstract")).length())).append("\n")
                                            .append("现在想问：").append(content);
                                }
                            } else {
                                prompt.append(content);
                            }
                            return prompt.toString();  // 返回构建的提示字符串
                        }).subscribeOn(Schedulers.boundedElastic())  // 指定在boundedElastic调度器上执行阻塞操作
                        .flatMapMany(prompt -> {
                            final long current = System.currentTimeMillis();

                            final Message message = buildUserMessage(prompt);
                            // 设置必要的字段以确保能够保存到Redis
                            message.setUserId(loginId);
                            message.setMsgUid(msgUid);
                            message.setTimestamp(current);

                            log.info("=== chatContOfStreamForPatent === 准备保存用户消息，msgUid: {}, userId: {}, 消息内容长度: {}",
                                    msgUid, loginId, prompt.length());

                            offerMessage(messagesHistory, message);

                            log.info("=== chatContOfStreamForPatent === 用户消息已保存，历史记录数量: {}", messagesHistory.size());
                            final ErnieRequest ernieRequest = ErnieRequest.builder()
                                    .messages(messagesHistory)
                                    .stream(true)
                                    .model("glm-4-flash-250414")
                                    .build();
                            final String token = getAccessToken();
                            return ChatUtils.historyFlux(getURL(), token, ernieRequest, messagesHistory, loginId, msgUid);
                        }));
    }

    @Override
    public Flux<ChatResponse> chatContOfStream(final String content, final String msgUid) {
        final HttpServletResponse response = HttpServletRequestUtil.getResponse();
        response.setHeader("Cache-Control", "no-cache");
        response.setHeader("Connection", "keep-alive");
        response.setHeader("Keep-Alive", "timeout=600");
        response.setHeader("Content-Encoding", "none");
        final Map<String, Queue<Message>> messageHistoryMap = getMessageHistoryMap();
        final Queue<Message> messagesHistory = messageHistoryMap.computeIfAbsent(
                msgUid, k -> new LinkedList<>()
        );
        return Mono.justOrEmpty(Tuples.of(content, msgUid))
                .filter(tuple -> StringUtils.isNotBlank(tuple.getT1()) && StringUtils.isNotBlank(tuple.getT2()))
                .switchIfEmpty(Mono.error(new BusinessException(ErrorCode.PARAMS_ERROR)))
                .flatMapMany(tuple -> Mono.fromCallable(() -> {  // 将阻塞操作封装在Mono.fromCallable()中
                            final StringBuilder prompt = new StringBuilder(128);
                            prompt.append(content);
                            return prompt.toString();  // 返回构建的提示字符串
                        }).subscribeOn(Schedulers.boundedElastic())  // 指定在boundedElastic调度器上执行阻塞操作
                        .flatMapMany(prompt -> {
                            final Message message = buildUserMessage(prompt);
                            offerMessage(messagesHistory, message);
                            final ErnieRequest ernieRequest = ErnieRequest.builder()
                                    .messages(messagesHistory)
                                    .stream(true)
                                    .model("glm-4-flash-250414")
                                    .build();
                            final String token = getAccessToken();
                            return ChatUtils.historyFluxNew(getURL(), token, ernieRequest, messagesHistory);
                        }));
    }


    // 存储msgUid和conversation_id的映射关系
    private static final Map<String, String> CONVERSATION_ID_MAP = new ConcurrentHashMap<>();


    /**
     * 从文本中提取<think>标签内的内容，去除标签本身
     *
     * @param text 原始文本
     * @return 提取的思考内容（不包含标签）
     */
    public static String extractThinkContent(String text) {
        if (text.contains("<think>")) {
            int thinkStart = text.indexOf("<think>") + 7; // +7 跳过<think>标签
            int thinkEnd = text.indexOf("</think>");
            if (thinkEnd == -1) {
                // 如果没有结束标签，取从<think>之后的部分
                return text.substring(thinkStart);
            } else {
                // 返回<think>和</think>之间的内容
                return text.substring(thinkStart, thinkEnd); 
            }
        }
        return "";
    }

    @Override
    public Flux<DpChatResponse> chatWithThink(final DpChatQueryRequest chatQueryRequest) {
        final String content = chatQueryRequest.getMsg();
        final String msgUid = chatQueryRequest.getMsgUid();
        final String agentType = chatQueryRequest.getExpertKey();

        log.info("=== chatWithThink 接口被调用 === msgUid: {}, agentType: {}, 消息内容: {}",
                msgUid, agentType, content.length() > 50 ? content.substring(0, 50) + "..." : content);
        // 优先使用请求中的conversationId，如果没有则从缓存中获取
        final String conversationId;
        if (StringUtils.isBlank(chatQueryRequest.getConversationId())) {
            // 从缓存中获取之前存储的conversation_id
            conversationId = CONVERSATION_ID_MAP.get(msgUid);
        } else {
            conversationId = chatQueryRequest.getConversationId();
        }
        final List<Long> fileIds = chatQueryRequest.getFileIds();
        final List<Long> currentFileIds = chatQueryRequest.getCurrentFileIds();
        final boolean regenerate = chatQueryRequest.getRegenerate() != null && chatQueryRequest.getRegenerate();
        final boolean editQuestion = chatQueryRequest.getEditQuestion() != null && chatQueryRequest.getEditQuestion();
        final long loginId = StpUtil.getLoginIdAsLong();
        final long current = System.currentTimeMillis();

        final Queue<Message> messagesHistory = RedisUtils.getDialogueQueueByMsgUid(loginId, msgUid);
        log.info("chatWithThink - 获取历史记录，msgUid: {}, userId: {}, 历史记录数量: {}",
                msgUid, loginId, messagesHistory.size());

        // 处理重新生成功能
        if (regenerate && !messagesHistory.isEmpty()) {
            log.info("用户请求重新生成回答，msgUid: {}", msgUid);
            // 移除最后一条AI回复（如果存在）
            if (messagesHistory.size() >= 2) {
                // 将队列转为列表，以便操作
                LinkedList<Message> messageList = new LinkedList<>(messagesHistory);
                // 检查最后一条消息是否为assistant的回复
                if (messageList.getLast().getRole() == RoleEnum.assistant) {
                    // 移除最后一条AI回复和最后一条用户问题
                    messageList.removeLast();
                    if (!messageList.isEmpty() && messageList.getLast().getRole() == RoleEnum.user) {
                        messageList.removeLast(); // 移除用户问题
                    }
                    // 更新Redis中的对话历史
                    RedisUtils.removeDialogueRecord(loginId, msgUid);
                    // 更新本地消息历史，确保顺序正确
                    messagesHistory.clear();
                    // 按照正确的队列顺序添加元素
                    for (final Message msg : messageList) {
                        messagesHistory.offer(msg);
                        RedisUtils.addDialogueRecord(loginId, msgUid, msg);
                    }
                }
            }
        }

        // 处理编辑问题功能
        if (editQuestion && !messagesHistory.isEmpty()) {
            log.info("用户请求编辑最后一个问题，msgUid: {}", msgUid);
            // 移除最后一组问答（如果存在）
            if (messagesHistory.size() >= 2) {
                // 将队列转为列表，以便操作
                final LinkedList<Message> messageList = new LinkedList<>(messagesHistory);
                // 检查最后一条消息是否为assistant的回复
                if (messageList.getLast().getRole() == RoleEnum.assistant) {
                    // 移除最后一条AI回复和用户问题
                    messageList.removeLast(); // 移除AI回复
                    if (!messageList.isEmpty() && messageList.getLast().getRole() == RoleEnum.user) {
                        messageList.removeLast(); // 移除用户问题
                    }
                    // 更新Redis中的对话历史
                    RedisUtils.removeDialogueRecord(loginId, msgUid);
                    // 更新本地消息历史，确保顺序正确
                    messagesHistory.clear();
                    // 按照正确的队列顺序添加元素
                    for (final Message msg : messageList) {
                        messagesHistory.offer(msg);
                        RedisUtils.addDialogueRecord(loginId, msgUid, msg);
                    }
                } else if (messageList.getLast().getRole() == RoleEnum.user) {
                    // 如果最后一条是用户消息（可能是刚提问但还没有回复）
                    messageList.removeLast(); // 移除用户问题
                    // 更新Redis中的对话历史
                    RedisUtils.removeDialogueRecord(loginId, msgUid);
                    // 更新本地消息历史，确保顺序正确
                    messagesHistory.clear();
                    // 按照正确的队列顺序添加元素
                    for (Message msg : messageList) {
                        messagesHistory.offer(msg);
                        RedisUtils.addDialogueRecord(loginId, msgUid, msg);
                    }
                }
            }
        }

        // 将type与msgUid组合，格式为：msgUid"type
        final String combinedMsgUid = msgUid + "\"" + (agentType != null ? agentType : "policy");

        final long count = sysChatPreserveService.count(Wrappers.<SysChatPreserve>lambdaQuery()
                .likeRight(SysChatPreserve::getMsgUid, msgUid + "\"") // 使用右模糊匹配查询msgUid部分
                .eq(SysChatPreserve::getUserId, loginId));
        if (count == 0) {
            // 写入历史记录标题和id到MySQL
            final SysChatPreserve chatPreserve = new SysChatPreserve();
            chatPreserve.setUserId(loginId);
            chatPreserve.setMsgUid(combinedMsgUid); // 存储组合后的msgUid
            chatPreserve.setTitle(content.length() > 20 ? content.substring(0, 20) : content);
            sysChatPreserveService.save(chatPreserve);
        }

        // 对应的表是sys_file,更新msgUid到文件sys_file的映射关系,现将所有subjectId为msgUid的文件记录中的subjectId删掉，然后再写入fileIds对应记录中的subjectId为msgUid
        if (CollUtil.isNotEmpty(fileIds)) {
            // 先清除原有关联
            sysFileService.update(
                    Wrappers.<SysFile>lambdaUpdate()
                            .set(SysFile::getSubjectId, null)
                            .eq(SysFile::getSubjectId, msgUid)
                            .eq(SysFile::getFileType, CHAT_DOC_TYPE)
            );
            // 建立新关联
            sysFileService.update(
                    Wrappers.<SysFile>lambdaUpdate()
                            .set(SysFile::getSubjectId, msgUid)
                            .in(SysFile::getFileId, fileIds)
                            .eq(SysFile::getFileType, CHAT_DOC_TYPE)
            );
        }
        // 对应的表是sys_file,更新对话id到文件sys_file的映射关系,md5由对话内容加时间戳生成
        if (CollUtil.isNotEmpty(currentFileIds)) {
            sysFileService.update(
                    Wrappers.<SysFile>lambdaUpdate()
                            .set(SysFile::getDialogueId, DigestUtils.md5DigestAsHex((content + current).getBytes(StandardCharsets.UTF_8)))
                            .in(SysFile::getFileId, currentFileIds)
                            .eq(SysFile::getFileType, CHAT_DOC_TYPE)
            );
        }

        return Mono.justOrEmpty(Tuples.of(content, msgUid))
                .filter(tuple -> StringUtils.isNotBlank(tuple.getT1()) && StringUtils.isNotBlank(tuple.getT2()))
                .switchIfEmpty(Mono.error(new BusinessException(ErrorCode.PARAMS_ERROR)))
                .flatMapMany(prompt -> {
                    try {
                        // 处理文件ID列表
                        List<UserFile> userFiles = null;
                        if (fileIds != null && !fileIds.isEmpty()) {
                            // 根据ID获取文件信息
                            List<SysFile> fileList = sysFileService.listByIds(fileIds);

                            if (!fileList.isEmpty()) {
                                userFiles = new ArrayList<>();
                                for (final SysFile file : fileList) {
                                    final String fileName = file.getOriName();
                                    // 直接使用文件内容字段，不再重新下载和解析
                                    final String fileContent = file.getFileContent();
                                    final String uploadTime = DateUtil.format(file.getCreateTime(), "yyyy-MM-dd HH:mm:ss");
                                    final UserFile userFile = new UserFile(fileName, fileContent, file.getUrl(), uploadTime);
                                    userFiles.add(userFile);
                                }
                            }
                        }

                        final Message message = buildUserMessage(content);
                        message.setUserId(loginId);
                        message.setMsgUid(msgUid);
                        message.setTimestamp(current);
                        message.setExpertKey(agentType);

                        // 确保用户消息能够正确保存到历史记录
                        log.info("chatWithThink - 准备保存用户消息，msgUid: {}, userId: {}, 消息内容长度: {}, 当前历史记录数量: {}",
                                msgUid, loginId, content.length(), messagesHistory.size());
                        try {
                            offerMessage(messagesHistory, message);
                            log.info("chatWithThink - 用户消息已保存到历史记录，msgUid: {}, userId: {}, 保存后历史记录数量: {}",
                                    msgUid, loginId, messagesHistory.size());
                        } catch (Exception e) {
                            log.error("chatWithThink - 保存用户消息到历史记录失败，msgUid: {}, userId: {}", msgUid, loginId, e);
                            // 即使保存失败也继续处理，避免影响对话流程
                        }

                        // 根据type选择对应的API密钥
                        final AgentConfig agentConfig = agentConfigService.getApiKeyByAgentKey(agentType);
                        if (agentConfig == null) {
                            return Mono.error(new BusinessException(ErrorCode.PARAMS_ERROR));
                        }
                        final String apiKey = agentConfig.getApiKey();

                        // 构建请求参数
                        final JSONObject requestBody = localModelComponent.buildChatRequestParam(content, "streaming");

                        // 直接返回dpStreamCompletion方法的结果
                        // dpStreamCompletion方法内部已经包含了订阅和发送数据的逻辑
                        return localModelComponent.dpStreamCompletion(
                                agentConfig.getDomainUrl() + agentConfig.getApiPath(),
                                apiKey,
                                requestBody,
                                conversationId,
                                messagesHistory,
                                msgUid,
                                loginId,
                                agentType
                        );

                    } catch (Exception e) {
                        log.error("处理对话时发生错误", e);
                        return Flux.error(new BusinessException(e.getMessage()));
                    }
                });
    }

    /**
     * 获取并检查队列，保留符合交替格式的部分
     */
    public Queue<Message> getMessageHistory(final String msgUid) {
        final long loginId = StpUtil.getLoginIdAsLong();
        final Queue<Message> messagesHistory = RedisUtils.getDialogueQueueByMsgUid(loginId, msgUid);
        
        // 检查队列中的数据是否符合要求，一个user，一个assistant
        if (!isAlternatingQueue(messagesHistory)) {
            // 创建一个新队列保存符合要求的消息
            Queue<Message> cleanedQueue = new LinkedList<>();
            // 初始状态，第一个应该是 user
            RoleEnum expectedRole = RoleEnum.user;
            
            // 遍历队列，只保留符合交替格式的部分
            for (Message message : new ArrayList<>(messagesHistory)) {
                if (message.getRole() != expectedRole) {
                    break; // 发现不符合格式的消息，停止保留
                }
                cleanedQueue.add(message);
                // 交替角色
                expectedRole = (expectedRole == RoleEnum.user) ? RoleEnum.assistant : RoleEnum.user;
            }
            
            // 清空原队列并添加符合格式的消息
            messagesHistory.clear();
            messagesHistory.addAll(cleanedQueue);
        }
        
        return messagesHistory;
    }

    /**
     * 可能追问的问题列表
     */
    public List<String> getPossibleQuestionList(final String msgUid, final Boolean reload) {
        final long loginId = StpUtil.getLoginIdAsLong();
        List<String> possibleQuestionList = Collections.emptyList();
        final Queue<Message> messagesHistory = RedisUtils.getDialogueQueueByMsgUid(loginId, msgUid);
        final Deque<Message> deque = (Deque<Message>) messagesHistory;
        deque.add(new Message(RoleEnum.user, "针对上面的问题和回答，你觉得用户可能还想问什么问题，只需要以json数组的格式返回可能的三个问题，比如[\"a问题\",\"b问题\",\"c问题\"],json数组中的对象直接就是问题的字符串，不需要加上类似question的key值\n", null));
        final ErnieRequest ernieRequest = ErnieRequest.builder()
                .messages(deque)
                .model("glm-4-flash-250414")
                .stream(false)
                .build();
        final HttpRequest httpRequest = HttpRequest.post(getURL())
                .header("Content-Type", "application/json")
                .header("Authorization", getCustomAccessToken())
                .body(JSON.toJSONString(ernieRequest));
        final HttpResponse execute = httpRequest.execute();
        final String jsonString = execute.body();
        final ChatResponse chatResponse = JSONObject.parseObject(jsonString, ChatResponse.class);
        final List<Choice> choices = chatResponse.getChoices();
        if (CollUtil.isEmpty(choices)) {
            return possibleQuestionList;
        }
        final Message message = choices.get(0).getMessage();
        if (message == null) {
            return possibleQuestionList;
        }
        final String content = message.getContent();
        final int startIndex = content.indexOf("[");
        final int endIndex = content.lastIndexOf("]") + 1;
        if (startIndex == -1 || endIndex == 0) {
            log.error("无法在字符串中找到JSON数据，返回默认问题");
        } else {
            final String substring = content.substring(startIndex, endIndex);
            final String cleanedJsonString = substring.replace("\n", "")
                    .replace("\\\"", "'");
            try {
                possibleQuestionList = JSON.parseArray(cleanedJsonString, String.class);
            } catch (final Exception e) {
                log.error("无法解析JSON数据，返回默认问题，大模型返回的数据为{}", content);
            }
        }
        // 清除提示语
        deque.pollLast();
        return possibleQuestionList;
    }

    public void validChatErnieRequest(final ChatErnieRequest request) {

        // 检查content不为空
        if (StringUtils.isBlank(request.getContent())) {
            throw new BusinessException(ErrorCode.PARAMS_ERROR, "content cannot be empty");
        }
        // 检查单个content长度
        if (request.getContent().length() > MAX_CONTENT_LENGTH) {
            throw new BusinessException(ErrorCode.PARAMS_ERROR, "content's length cannot be more than 2000");
        }
        // 检查temperature和topP不都有值
        if (request.getTemperature() != null && request.getTopP() != null) {
            log.warn("Temperature and topP cannot both have value");
        }
        // 检查temperature范围
        if (request.getTemperature() != null && (request.getTemperature() <= 0 || request.getTemperature() > 1.0)) {
            throw new BusinessException(ErrorCode.PARAMS_ERROR, "temperature should be in (0, 1]");
        }
        // 检查topP范围
        if (request.getTopP() != null && (request.getTopP() < 0 || request.getTopP() > 1.0)) {
            throw new BusinessException(ErrorCode.PARAMS_ERROR, "topP should be in [0, 1]");
        }
        // 检查penaltyScore范围
        if (request.getTemperature() != null && (request.getPenaltyScore() < 1.0 || request.getPenaltyScore() > 2.0)) {
            throw new BusinessException(ErrorCode.PARAMS_ERROR, "penaltyScore should be in [1, 2]");
        }

    }
}
