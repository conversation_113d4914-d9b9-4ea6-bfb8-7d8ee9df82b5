package com.quantchi.knowledge.ai.bo;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 筛选记录保存表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-21
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "筛选项保存类")
public class FilterConditionPreserveBO {

    @ApiModelProperty("保存类型")
    private String preserveType;

    @ApiModelProperty("筛选类型，1普通筛选/2高级筛选/3头条关注")
    private Integer filterType;

    @ApiModelProperty("保存数据")
    private JSONObject preserveData;

}
