package com.quantchi.knowledge.ai.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2023/9/15 17:40
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QaPromptPageBO extends PageBO {

    @ApiModelProperty("种类")
    private String category;

    @ApiModelProperty("1问答/2报告/3智能体")
    private Integer type = 1;

    @ApiModelProperty("智能体 key")
    private String expertKey;
}
