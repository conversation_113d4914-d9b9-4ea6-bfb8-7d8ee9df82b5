package com.quantchi.knowledge.ai.common;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.quantchi.knowledge.ai.entity.Message;
import com.quantchi.knowledge.ai.entity.response.ChatResponse;
import com.quantchi.knowledge.ai.entity.response.Choice;
import com.quantchi.knowledge.ai.entity.response.Delta;
import com.quantchi.knowledge.ai.subscriber.CommonSubscriber;
import com.quantchi.knowledge.center.bean.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.codec.DecodingException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Objects;
import java.util.Queue;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;

/**
 * @date 2023/7/21
 */
@Slf4j
public class ChatUtils {

    private static final WebClient WEB_CLIENT = WebClient.builder()
            .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
            .build();

    /**
     * 流式请求 POST
     *
     * @param url         请求地址
     * @param accessToken accessToken
     * @param request     请求类
     * @return Flux<T>
     */
    public static <T> Flux<T> fluxChatPost(final String url, final String accessToken, final Object request, final Class<T> type) {
        validateParams(url, accessToken, request, type);
        log.info("fluxURL => {}", url);

        return buildWebClient(url).post()
                .body(BodyInserters.fromValue(JSON.toJSONString(request)))
                .header("Authorization", accessToken)
                .accept(MediaType.TEXT_EVENT_STREAM)
                .retrieve()
                .bodyToFlux(type)
                .onErrorResume(DecodingException.class, e -> {
                    if (e.getMessage().contains("Cannot deserialize value of type")) {
                        log.warn("Skipping invalid response segment: " + e.getMessage());
                        return Flux.empty();  // 忽略无效的部分，继续处理
                    }
                    return Flux.error(e);  // 其他错误继续抛出
                })
                .doOnError(WebClientResponseException.class, handleWebClientError());
    }

    /**
     * 流式请求 POST
     *
     * @param url         请求地址
     * @param accessToken accessToken
     * @param request     请求类
     * @return Flux<T>
     */
    public static <T> Flux<T> fluxChatPostWithBearerToken(final String url, final String accessToken, final Object request, final Class<T> type) {
        validateParams(url, accessToken, request, type);
        final String jsonString = JSON.toJSONString(request);
        log.info("fluxURL => {},jsonString {}", url, jsonString);

        return buildWebClient(url).post()
                .body(BodyInserters.fromValue(jsonString))
                .header("Authorization", "Bearer " + accessToken)
                .accept(MediaType.TEXT_EVENT_STREAM)
                .retrieve()
                .bodyToFlux(type)
                .onErrorResume(DecodingException.class, e -> {
                    if (e.getMessage().contains("Cannot deserialize value of type")) {
                        log.warn("Skipping invalid response segment: " + e.getMessage());
                        return Flux.empty();  // 忽略无效的部分，继续处理
                    }
                    return Flux.error(e);  // 其他错误继续抛出
                })
                .doOnError(WebClientResponseException.class, handleWebClientError());
    }

    public static <T> Flux<ChatResponse> historyFlux(final String url, final String token, final T request, final Queue<Message> messagesHistory) {
        return historyFlux(url, token, request, messagesHistory, null, null);
    }

    public static <T> Flux<ChatResponse> historyFlux(final String url, final String token, final T request,
                                                    final Queue<Message> messagesHistory, final Long userId, final String msgUid) {
        return Flux.create(emitter -> {
            final CommonSubscriber subscriber = new CommonSubscriber(emitter, messagesHistory, userId, msgUid);
            final Flux<ChatResponse> chatResponseFlux = ChatUtils.fluxChatPost(
                    url, token, request, ChatResponse.class
            );
            // 在这里对每个 ChatResponse 进行处理
            final AtomicInteger flag = new AtomicInteger(0);
            final Flux<ChatResponse> processedFlux = chatResponseFlux.map(chatResponse -> {
                // 对 chatResponse 进行自定义处理
                // 例如，修改内容、添加日志、过滤等
                final List<Choice> choices = chatResponse.getChoices();
                if (CollUtil.isNotEmpty(choices)) {
                    final Choice choice = choices.get(0);
                    final String finishReason = choice.getFinishReason();
                    if (Objects.equals(finishReason, "stop")) {
                        chatResponse.setIsEnd(true);
                    } else {
                        final Delta delta = choice.getDelta();
                        if (delta != null) {
                            final Object content = delta.getContent();
                            if (flag.get() == 0 && Objects.equals(content, "\n")) {
                                chatResponse.setContent("");
                                flag.set(1);
                            } else {
                                chatResponse.setContent((content == null) ? "" : content.toString());
                            }
                            chatResponse.setChoices(null);
                        }
                    }
                }
                return chatResponse; // 返回处理后的 chatResponse
            });
            processedFlux.subscribe(subscriber);
            emitter.onDispose(subscriber);
        });
    }

    private static WebClient buildWebClient(final String baseUrl) {
        return WEB_CLIENT.mutate()
                .baseUrl(baseUrl)
                .build();
    }

    private static <T> void validateParams(final String url, final String accessToken, final Object request, final Class<T> type) {
        if (StringUtils.isBlank(url) || StringUtils.isBlank(accessToken) || request == null || type == null) {
            throw new BusinessException(ErrorCode.PARAMS_ERROR);
        }
    }

    private static Consumer<Throwable> handleWebClientError() {
        return err -> {
            log.error("请求错误 => {} {}", err instanceof WebClientResponseException
                    ? ((WebClientResponseException) err).getStatusCode()
                    : "Unknown", err.getMessage());
            throw new BusinessException(ErrorCode.SYSTEM_NET_ERROR);
        };
    }

    public static <T> Flux<ChatResponse> historyFluxNew(String url, String token, T request, Queue<Message> messagesHistory) {
        return Flux.create(emitter -> {
            CommonSubscriber subscriber = new CommonSubscriber(emitter, messagesHistory);
            Flux<ChatResponse> chatResponseFlux = ChatUtils.fluxChatPost(
                    url, token, request, ChatResponse.class
            );
            // 在这里对每个 ChatResponse 进行处理
            final AtomicInteger flag = new AtomicInteger(0);
            Flux<ChatResponse> processedFlux = chatResponseFlux.map(chatResponse -> {
                // 对 chatResponse 进行自定义处理
                // 例如，修改内容、添加日志、过滤等
                final List<Choice> choices = chatResponse.getChoices();
                if (CollUtil.isNotEmpty(choices)) {
                    final Choice choice = choices.get(0);
                    final String finishReason = choice.getFinishReason();
                    if (Objects.equals(finishReason, "stop")) {
                        chatResponse.setIsEnd(true);
                    } else {
                        final Delta delta = choice.getDelta();
                        if (delta != null) {
                            final Object content = delta.getContent();
                            if (flag.get() == 0 && Objects.equals(content, "\n")) {
                                chatResponse.setContent("");
                                flag.set(1);
                            } else {
                                chatResponse.setContent((content == null) ? "" : content.toString());
                            }
                            chatResponse.setChoices(null);
                        }
                    }
                }
                return chatResponse; // 返回处理后的 chatResponse
            });
            processedFlux.subscribe(subscriber);
            emitter.onDispose(subscriber);
        });
    }

}
