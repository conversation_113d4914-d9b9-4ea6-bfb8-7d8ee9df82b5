package com.quantchi.knowledge.ai.common;

import com.quantchi.knowledge.ai.entity.chatmodel.ChatErnieRequest;
import com.quantchi.knowledge.ai.entity.request.ErnieRequest;
import com.quantchi.knowledge.center.bean.exception.BusinessException;

import java.util.Objects;

/**
 * 类型转换工具类
 *
 * @date 2023/5/27
 */
public class ConvertUtils {

    public static ErnieRequest.ErnieRequestBuilder toErnieRequest(final ChatErnieRequest chatRequest) {

        Objects.requireNonNull(chatRequest, "ChatErnieRequest is null");
        if (chatRequest.getContent() == null) {
            throw new BusinessException(ErrorCode.PARAMS_ERROR, "content is null!");
        }
        return ErnieRequest.builder()
                .userId(chatRequest.getUserId())
                .messages(WenXinUtils.buildUserMessageQueue(chatRequest.getContent()))
                .temperature(chatRequest.getTemperature())
                .topP(chatRequest.getTopP())
                .penaltyScore(chatRequest.getPenaltyScore());
    }

}