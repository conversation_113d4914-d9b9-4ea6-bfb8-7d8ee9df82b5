package com.quantchi.knowledge.ai.common;

import cn.hutool.core.text.CharSequenceUtil;
import com.quantchi.knowledge.ai.entity.Message;
import com.quantchi.knowledge.center.bean.exception.BusinessException;
import com.quantchi.knowledge.center.util.RedisUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Deque;
import java.util.LinkedList;
import java.util.Queue;

import static com.quantchi.knowledge.ai.common.Constant.MAX_TOTAL_LENGTH;

/**
 * @date 2023/7/23
 */
@Slf4j
public class WenXinUtils {

    public static Queue<Message> buildUserMessageQueue(final String content) {
        if (content == null) {
            throw new BusinessException(ErrorCode.PARAMS_ERROR, "content is null");
        }
        final Queue<Message> messageQueue = new LinkedList<>();
        final Message message = buildUserMessage(content);
        messageQueue.offer(message);
        return messageQueue;
    }

    public static Message buildUserMessage(final String content) {
        if (content == null) {
            throw new BusinessException(ErrorCode.PARAMS_ERROR, "content is null");
        }
        return new Message(RoleEnum.user, content, null);
    }

    public static Message buildAssistantMessage(final String content, final String reasoningContent) {
        if (content == null) {
            throw new BusinessException(ErrorCode.PARAMS_ERROR, "content is null");
        }
        return new Message(RoleEnum.assistant, content, reasoningContent);
    }

    /**
     * 向历史消息中添加消息
     *
     * @param messagesHistory 历史消息队列
     * @param message         需添加的Message
     */
    public static void offerMessage(final Queue<Message> messagesHistory, final Message message) {

        if (messagesHistory == null || message == null) {
            throw new BusinessException(ErrorCode.PARAMS_ERROR);
        }
        // 检查队列中的数据是否符合要求，确保交替出现user和assistant
        if (!isAlternatingQueue(messagesHistory)) {
            // 修复队列而不是清空它
            fixMessageQueue(messagesHistory);
        }
        // 检查要添加的消息是否为空
        if (CharSequenceUtil.isBlank(message.getContent()) && CharSequenceUtil.isBlank(message.getReasoningContent())) {
            log.error("message content is blank, messagesHistory is {}", messagesHistory);
            // 内容为空时，当队列大小为偶数时，那么要添加的是user，此时不添加，直接返回，当队列大小是奇数时，那么要添加的是assistant，此时需要把队列末端的user信息给清除掉
            if (messagesHistory.size() % 2 != 0) {
                final Deque<Message> deque = (Deque<Message>) messagesHistory;
                deque.pollLast();
            }
            return;
        }
        messagesHistory.offer(message);
        if (message.getUserId() != null && message.getMsgUid() != null) {
            RedisUtils.addDialogueRecord(message.getUserId(), message.getMsgUid(), message);
        }

        if (message.getRole() == RoleEnum.assistant) {
            return;
        }

        int totalLength = 0;
        for (final Message msg : messagesHistory) {
            if (msg.getRole() == RoleEnum.user) {
                totalLength += msg.getContent().length();
            }
        }

        while (totalLength > MAX_TOTAL_LENGTH) {
            final Message firstMessage = messagesHistory.poll();
            final Message secondMessage = messagesHistory.poll();
            if (firstMessage != null && secondMessage != null) {
                totalLength -= (firstMessage.getContent().length() + secondMessage.getContent().length());
            }
        }

    }

    /**
     * 修复消息队列，确保消息交替出现（user-assistant-user...）
     * 
     * @param messagesHistory 需要修复的消息队列
     */
    private static void fixMessageQueue(final Queue<Message> messagesHistory) {
        if (messagesHistory.isEmpty()) {
            return;
        }
        
        log.warn("检测到不符合交替顺序的消息队列，开始修复");
        
        // 创建临时队列来存储修复后的消息
        final Deque<Message> fixedQueue = new LinkedList<>();
        RoleEnum expectedRole = RoleEnum.user; // 期望的第一个角色是user
        
        // 遍历原队列中的所有消息
        for (Message msg : new ArrayList<>(messagesHistory)) {
            // 如果当前消息符合期望角色，添加到新队列并切换期望角色
            if (msg.getRole() == expectedRole) {
                fixedQueue.offer(msg);
                expectedRole = (expectedRole == RoleEnum.user) ? RoleEnum.assistant : RoleEnum.user;
            } else {
                // 不符合期望角色，跳过该消息
                log.debug("跳过不符合交替顺序的消息: {}", msg);
            }
        }
        
        // 清空原队列并将修复后的队列内容复制回去
        messagesHistory.clear();
        messagesHistory.addAll(fixedQueue);
        
        log.info("消息队列修复完成，修复后队列大小: {}", messagesHistory.size());
    }

    public static boolean  isAlternatingQueue(final Queue<Message> messagesHistory) {
        if (messagesHistory.isEmpty()) {
            return true; // 空队列视为满足条件
        }

        // 初始状态，检查第一个应该是 user
        RoleEnum expectedRole = RoleEnum.user;

        for (final Message message : messagesHistory) {
            if (message.getRole() != expectedRole) {
                return false; // 找到不满足条件的消息
            }
            // 交替角色
            expectedRole = (expectedRole == RoleEnum.user) ? RoleEnum.assistant : RoleEnum.user;
        }

        return true; // 全部消息都满足条件
    }

}
