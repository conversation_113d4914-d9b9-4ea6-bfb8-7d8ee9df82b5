package com.quantchi.knowledge.center.config;

import io.netty.channel.ChannelOption;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * WebClient 配置类
 * 专门用于流式API调用的WebClient配置
 * 
 * <AUTHOR>
 * @since 2024-12-30
 */
@Slf4j
@Configuration
public class WebClientConfig {

    /**
     * 创建用于流式API调用的WebClient.Builder
     * 配置了适合流式处理的超时和缓冲区设置
     */
    @Bean
    public WebClient.Builder webClientBuilder() {
        // 配置HttpClient
        HttpClient httpClient = HttpClient.create()
                // 连接超时
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 30000)
                // 响应超时 - 对于流式接口需要设置较长的超时时间
                .responseTimeout(Duration.ofMinutes(15))
                .doOnConnected(conn -> 
                    conn.addHandlerLast(new ReadTimeoutHandler(15, TimeUnit.MINUTES))
                        .addHandlerLast(new WriteTimeoutHandler(30, TimeUnit.SECONDS))
                );

        // 配置交换策略，增加缓冲区大小以处理大量流式数据
        ExchangeStrategies strategies = ExchangeStrategies.builder()
                .codecs(configurer -> {
                    // 设置内存中缓冲的最大字节数 (默认256KB，这里设置为10MB)
                    configurer.defaultCodecs().maxInMemorySize(10 * 1024 * 1024);
                    // 启用流式处理
                    configurer.defaultCodecs().enableLoggingRequestDetails(true);
                })
                .build();

        return WebClient.builder()
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .exchangeStrategies(strategies);
    }
}
