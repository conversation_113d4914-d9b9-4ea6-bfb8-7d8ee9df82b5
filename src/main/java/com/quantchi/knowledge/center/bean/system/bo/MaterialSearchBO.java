package com.quantchi.knowledge.center.bean.system.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 素材检索请求BO
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
@Data
@ApiModel("素材检索请求")
public class MaterialSearchBO {

    @ApiModelProperty(value = "用户角色", example = "01", notes = "01-普通用户 02-vip用户")
    private String userRole = "01";

    @ApiModelProperty(value = "报告ID", example = "123")
    private Long reportId;

    @ApiModelProperty(value = "查询关键词", example = "杭州", notes = "为空时默认查询报告的主题和大纲")
    private String query;
}
