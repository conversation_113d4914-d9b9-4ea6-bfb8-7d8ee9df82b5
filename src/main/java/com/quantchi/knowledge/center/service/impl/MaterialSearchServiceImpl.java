package com.quantchi.knowledge.center.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.quantchi.knowledge.center.bean.entity.SysAiReport;
import com.quantchi.knowledge.center.bean.entity.SysReport;
import com.quantchi.knowledge.center.bean.enums.EsIndexEnum;
import com.quantchi.knowledge.center.bean.exception.BusinessException;
import com.quantchi.knowledge.center.bean.system.bo.MaterialSearchBO;
import com.quantchi.knowledge.center.bean.system.vo.MaterialItemVO;
import com.quantchi.knowledge.center.bean.vo.TocEntryVO;
import com.quantchi.knowledge.center.helper.ElasticsearchHelper;
import com.quantchi.knowledge.center.service.IMaterialSearchService;
import com.quantchi.knowledge.center.service.ISysAiReportService;
import com.quantchi.knowledge.center.service.ISysReportService;
import com.quantchi.knowledge.center.util.ExternalReportApiClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.index.query.IdsQueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.fetch.subphase.FetchSourceContext;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 素材检索服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MaterialSearchServiceImpl implements IMaterialSearchService {

    private final ExternalReportApiClient externalReportApiClient;
    private final ISysReportService sysReportService;
    private final ISysAiReportService sysAiReportService;
    private final ElasticsearchHelper elasticsearchHelper;

    @Override
    public List<MaterialItemVO> materialSearch(final MaterialSearchBO searchBO) {
        try {
            String query = searchBO.getQuery();
            
            // 如果query为空且reportId不为空，则根据报告信息构建query
            if (CharSequenceUtil.isBlank(query) && searchBO.getReportId() != null) {
                query = buildQueryFromReport(searchBO.getReportId());
            }
            
            // 如果query仍为空，抛出异常
            if (CharSequenceUtil.isBlank(query)) {
                throw new BusinessException("查询关键词不能为空");
            }

            // 调用外部素材检索API
            final JSONObject response = externalReportApiClient.callMaterialSearchApi(
                    StpUtil.getLoginIdAsString(),
                    searchBO.getUserRole(),
                    query
            );

            // 转换数据列表
            final List<MaterialItemVO> materialItems = new ArrayList<>();
            final JSONArray dataArray = response.getJSONArray("data");
            if (dataArray != null) {
                // 收集所有的 doc_id 用于批量查询研报信息
                final List<String> docIds = new ArrayList<>();

                for (int i = 0; i < dataArray.size(); i++) {
                    final JSONObject item = dataArray.getJSONObject(i);
                    final MaterialItemVO materialItem = new MaterialItemVO();
                    materialItem.setChunkId(item.getLong("chunk_id"));
                    materialItem.setDocId(item.getString("doc_id"));
                    materialItem.setChunk(item.getString("chunk"));
                    materialItems.add(materialItem);

                    // 收集非空的 doc_id
                    final String docId = item.getString("doc_id");
                    if (CharSequenceUtil.isNotBlank(docId)) {
                        docIds.add(docId);
                    }
                }

                // 批量查询研报信息并设置到对应的 MaterialItemVO 中
                if (!docIds.isEmpty()) {
                    enrichMaterialItemsWithReportInfo(materialItems, docIds);
                }
            }

            return materialItems;

        } catch (final Exception e) {
            log.error("素材检索失败", e);
            throw new BusinessException("素材检索失败：" + e.getMessage());
        }
    }

    /**
     * 根据报告ID构建查询字符串
     *
     * @param reportId 报告ID
     * @return 构建的查询字符串
     */
    private String buildQueryFromReport(final Long reportId) {
        try {
            // 获取报告基础信息
            final SysReport report = sysReportService.getById(reportId);
            if (report == null) {
                throw new BusinessException("报告不存在");
            }

            final StringBuilder queryBuilder = new StringBuilder();
            
            // 添加主题信息
            if (CharSequenceUtil.isNotBlank(report.getTitle())) {
                queryBuilder.append("主题：《").append(report.getTitle()).append("》\n");
            }

            // 如果是AI报告，获取大纲信息
            if (report.getType() != null && report.getType() == 2) { // 2表示AI报告
                final SysAiReport aiReport = sysAiReportService.getById(reportId);
                if (aiReport != null && CharSequenceUtil.isNotBlank(aiReport.getOutline())) {
                    // 将大纲JSON转换为markdown格式
                    final String markdownOutline = convertOutlineToMarkdown(aiReport.getOutline());
                    if (CharSequenceUtil.isNotBlank(markdownOutline)) {
                        queryBuilder.append("该主题大纲：《").append(markdownOutline).append("》");
                    }
                }
            }

            return queryBuilder.toString();

        } catch (final Exception e) {
            log.error("根据报告ID构建查询字符串失败，reportId: {}", reportId, e);
            throw new BusinessException("构建查询字符串失败：" + e.getMessage());
        }
    }

    /**
     * 将大纲JSON转换为markdown格式
     *
     * @param outlineJson 大纲JSON字符串
     * @return markdown格式的大纲
     */
    private String convertOutlineToMarkdown(final String outlineJson) {
        try {
            // 解析JSON为TocEntryVO列表
            final List<TocEntryVO> tocEntries = JSONObject.parseArray(outlineJson, TocEntryVO.class);
            if (tocEntries == null || tocEntries.isEmpty()) {
                return "";
            }

            final StringBuilder markdown = new StringBuilder();
            convertTocEntriesToMarkdown(tocEntries, markdown, 1);
            return markdown.toString();

        } catch (final Exception e) {
            log.error("转换大纲为markdown格式失败", e);
            return "";
        }
    }

    /**
     * 递归转换TocEntry列表为markdown格式
     *
     * @param tocEntries TocEntry列表
     * @param markdown markdown构建器
     * @param level 标题级别
     */
    private void convertTocEntriesToMarkdown(final List<TocEntryVO> tocEntries,
                                           final StringBuilder markdown,
                                           final int level) {
        for (final TocEntryVO entry : tocEntries) {
            // 使用entry自身的level，如果没有则使用传入的level
            final int actualLevel = entry.getLevel() != null ? entry.getLevel() : level;

            // 添加标题
            for (int i = 0; i < actualLevel; i++) {
                markdown.append("#");
            }
            markdown.append(" ").append(entry.getTitle()).append("\n");

            // 递归处理子项
            if (entry.getChildren() != null && !entry.getChildren().isEmpty()) {
                convertTocEntriesToMarkdown(entry.getChildren(), markdown, actualLevel + 1);
            }

            markdown.append("\n");
        }
    }

    /**
     * 批量根据 doc_id 从 ES 研报库中获取 title 和 source，并设置到对应的 MaterialItemVO 中
     *
     * @param materialItems 素材项列表
     * @param docIds 文档ID列表
     */
    private void enrichMaterialItemsWithReportInfo(final List<MaterialItemVO> materialItems, final List<String> docIds) {
        try {
            if (docIds.isEmpty()) {
                return;
            }

            // 批量从 ES 研报库中查询 title 和 source 字段
            final Map<String, Map<String, Object>> reportInfoMap = getReportInfoByIds(docIds);

            // 将查询到的研报信息设置到对应的 MaterialItemVO 中
            for (final MaterialItemVO materialItem : materialItems) {
                final String docId = materialItem.getDocId();
                if (CharSequenceUtil.isNotBlank(docId) && reportInfoMap.containsKey(docId)) {
                    final Map<String, Object> reportInfo = reportInfoMap.get(docId);

                    final Object title = reportInfo.get("title");
                    final Object source = reportInfo.get("source");

                    if (title instanceof String) {
                        materialItem.setTitle((String) title);
                    }

                    if (source instanceof String) {
                        materialItem.setSource((String) source);
                    }
                }
            }

        } catch (final Exception e) {
            log.error("批量获取研报信息失败，docIds: {}", docIds, e);
            // 发生异常时不影响主流程，只记录日志
        }
    }

    /**
     * 批量根据ID列表从ES研报库中获取文档信息
     *
     * @param docIds 文档ID列表
     * @return 文档ID到文档信息的映射
     */
    private Map<String, Map<String, Object>> getReportInfoByIds(final List<String> docIds) {
        final Map<String, Map<String, Object>> result = new HashMap<>();

        try {
            // 使用 IdsQueryBuilder 进行批量查询
            final SearchRequest searchRequest = new SearchRequest(EsIndexEnum.REPORT.getEsIndex());
            final SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

            // 构建批量ID查询
            final IdsQueryBuilder idsQuery = new IdsQueryBuilder();
            for (final String docId : docIds) {
                idsQuery.addIds(docId);
            }
            searchSourceBuilder.query(idsQuery);

            // 只查询需要的字段
            final String[] includes = {"title", "source"};
            final FetchSourceContext fetchSourceContext = new FetchSourceContext(true, includes, null);
            searchSourceBuilder.fetchSource(fetchSourceContext);

            // 设置查询大小
            searchSourceBuilder.size(docIds.size());
            searchRequest.source(searchSourceBuilder);

            // 执行查询
            final SearchResponse response = elasticsearchHelper.getRestHighLevelClient()
                    .search(searchRequest, RequestOptions.DEFAULT);

            // 处理查询结果
            final SearchHits hits = response.getHits();
            for (final SearchHit hit : hits) {
                result.put(hit.getId(), hit.getSourceAsMap());
            }

        } catch (final Exception e) {
            log.error("批量查询研报信息失败，docIds: {}", docIds, e);
        }

        return result;
    }
}
