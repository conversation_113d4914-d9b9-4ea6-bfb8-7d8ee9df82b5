package com.quantchi.knowledge.center.service;

import com.quantchi.knowledge.center.bean.system.bo.MaterialSearchBO;
import com.quantchi.knowledge.center.bean.system.vo.MaterialItemVO;

import java.util.List;

/**
 * 素材检索服务接口
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
public interface IMaterialSearchService {

    /**
     * 素材检索
     *
     * @param searchBO 检索请求参数
     * @return 检索结果
     */
    List<MaterialItemVO> materialSearch(MaterialSearchBO searchBO);
}
