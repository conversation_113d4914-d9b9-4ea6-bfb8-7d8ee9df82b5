spring:
  mvc:
    hiddenmethod:
      filter:
        enabled: true
  servlet:
    multipart:
      enabled: true
      file-size-threshold: 10MB
      max-file-size: 10MB
      max-request-size: 10MB
  profiles:
    active: testk8s
    mvc:
      async:
        timeout: 3600000  # 1小时，单位毫秒
  jackson:
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss
    # 支持Java 8时间类型
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
server:
  servlet:
    context-path: /api
  port: 8612
  tomcat:
    connection-timeout: 300000  # 5分钟，单位毫秒
    uri-encoding: UTF-8
    max-http-form-post-size: -1
    max-swallow-size: -1
  max-http-header-size: 1024000
  undertow:
    max-http-post-size: -1

mybatis-plus:
  mapper-locations: classpath:/mapper/**/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.quantchi.knowledge.center.entity
  global-config:
    # 数据库相关配置
    db-config:
      #主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID",ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: auto
      #字段策略 IGNORED:"忽略判断",NOT_NULL:"非 NULL 判断"),NOT_EMPTY:"非空判断"
      field-strategy: not_empty
      #驼峰下划线转换
      table-underline: true
      db-type: mysql
  # 原生配置
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true

logging:
  config: classpath:logback-spring.xml
  level:
    # 关闭HikariCP的DEBUG日志，避免过多连接池维护日志
    com.zaxxer.hikari: INFO

# 存储类型选择：oss或minio
storage:
  type: oss

file:
  preUrl: https://192.168.1.68:30897
  path: /api/file/download/
  save:
    choose: 0  #0为oss 1为本地

# Sa-Token配置
sa-token:
  # token 名称 (同时也是cookie名称)
  token-name: Authorization
  # token 有效期，单位s 目前设置为8小时, -1代表永不过期
  timeout: 604800
  # token 临时有效期 (指定时间内无操作就视为token过期) 单位: 秒
  activity-timeout: -1
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: false
  # token风格
  token-style: simple-uuid
  # 是否输出操作日志
  is-log: false
  maxLoginCount: 100
  is-read-cookie: true    # 是否从cookie中读取token
  is-read-header: true    # 是否从header中读取token

model:
  param:
    oneApiUrl: https://open.bigmodel.cn/api/paas/v4/chat/completions
    dpKeywordUrl: 'http://183.129.178.195:8011/api/v1/reports/keywords'
    dpAccessToken: 'd7c79932ce0df80ca1e38ce258c56388.LYDLc6yvQO2YhFZo'
    dpReportWriteUrl: 'http://183.129.178.195:8011/api/v1/reports/writing'
    externalReportBaseUrl: 'http://192.168.1.68:32445'

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# 阿里云 OSS配置
aliyun:
  oss:
    keyId: LTAI5tMMZwjRwZSvSpxMTJX3
    keySecret: ******************************
    bucketName: icir
    endpoint: oss-cn-zhangjiakou.aliyuncs.com
    
# MinIO配置
minio:
  endpoint: http://192.168.1.69:9000
  accessKey: iciruser
  secretKey: ******************************
  bucketName: icir
  secure: false

dingding:
  companyName: "杭州量知数据科技有限公司"
  clientId: 'dingiofu164hr5hnd9nr'
  clientSecret: '9t_9DEYwENjN4bmrgZHwZH3N79wDK4gHdkbyLCh0iieCR2sR4PxA39K1attJrTUB'

log:
  print:
    flag: false

interface:
  rate: 1

report:
  url: 'http://icir-reporter:8331/model/generate_report'
data:
  prefix: 'http://icir-data:8353/data'
  abnormalUpdateUrl: '/abnormal_update'
  baseinfoUpdateUrl: '/baseinfo_update'
  illegalUpdateUrl: '/illegal_update'
  punishUpdateUrl: '/punish_update'
  shareholderUpdateUrl: '/shareholder_update'
  branchUpdateUrl: '/branch_update'
  changeUpdateUrl: '/change_update'
  customerUpdateUrl: '/customer_update'
  investUpdateUrl: '/invest_update'
  leaderUpdateUrl: '/leader_update'
  supplierUpdateUrl: '/supplier_update'

oss:
  profiles:
    active: test

api:
  token:
    fixed-token: 8b2b1240fba94584a3eb296b04cda9c9
    enabled: true

# Dify接口配置
dify:
  api:
    parameters-path: /v1/parameters
