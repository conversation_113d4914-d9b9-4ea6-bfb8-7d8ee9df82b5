package com.quantchi.knowledge.center.service;

import com.quantchi.knowledge.center.bean.enums.EsIndexEnum;
import com.quantchi.knowledge.center.bean.system.vo.MaterialItemVO;
import com.quantchi.knowledge.center.helper.ElasticsearchHelper;
import com.quantchi.knowledge.center.service.impl.MaterialSearchServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * MaterialSearchService 研报信息补充功能测试
 */
public class MaterialSearchServiceEnrichmentTest {

    @Mock
    private ElasticsearchHelper elasticsearchHelper;

    private MaterialSearchServiceImpl materialSearchService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        materialSearchService = new MaterialSearchServiceImpl(null, null, null, elasticsearchHelper);
    }

    @Test
    void testEnrichMaterialItemWithReportInfo() throws Exception {
        // 准备测试数据
        MaterialItemVO materialItem = new MaterialItemVO();
        materialItem.setDocId("test-doc-id");
        materialItem.setChunkId(123L);
        materialItem.setChunk("测试素材内容");

        // 模拟 ES 返回的研报信息
        Map<String, Object> reportInfo = new HashMap<>();
        reportInfo.put("title", "测试研报标题");
        reportInfo.put("source", "测试研报来源");

        // 配置 mock
        when(elasticsearchHelper.getDataById(
                eq(EsIndexEnum.REPORT.getEsIndex()),
                eq("test-doc-id"),
                any(String[].class),
                isNull()
        )).thenReturn(reportInfo);

        // 使用反射调用私有方法
        Method method = MaterialSearchServiceImpl.class.getDeclaredMethod(
                "enrichMaterialItemWithReportInfo",
                MaterialItemVO.class
        );
        method.setAccessible(true);
        method.invoke(materialSearchService, materialItem);

        // 验证结果
        assertEquals("测试研报标题", materialItem.getTitle());
        assertEquals("测试研报来源", materialItem.getSource());
        assertEquals("test-doc-id", materialItem.getDocId());
        assertEquals(Long.valueOf(123L), materialItem.getChunkId());
        assertEquals("测试素材内容", materialItem.getChunk());
    }

    @Test
    void testEnrichMaterialItemWithEmptyDocId() throws Exception {
        // 准备测试数据 - docId 为空
        MaterialItemVO materialItem = new MaterialItemVO();
        materialItem.setDocId("");
        materialItem.setChunkId(123L);
        materialItem.setChunk("测试素材内容");

        // 使用反射调用私有方法
        Method method = MaterialSearchServiceImpl.class.getDeclaredMethod(
                "enrichMaterialItemWithReportInfo",
                MaterialItemVO.class
        );
        method.setAccessible(true);
        method.invoke(materialSearchService, materialItem);

        // 验证结果 - title 和 source 应该为 null
        assertNull(materialItem.getTitle());
        assertNull(materialItem.getSource());
    }

    @Test
    void testEnrichMaterialItemWithNullReportInfo() throws Exception {
        // 准备测试数据
        MaterialItemVO materialItem = new MaterialItemVO();
        materialItem.setDocId("test-doc-id");
        materialItem.setChunkId(123L);
        materialItem.setChunk("测试素材内容");

        // 配置 mock 返回 null
        when(elasticsearchHelper.getDataById(
                eq(EsIndexEnum.REPORT.getEsIndex()),
                eq("test-doc-id"),
                any(String[].class),
                isNull()
        )).thenReturn(null);

        // 使用反射调用私有方法
        Method method = MaterialSearchServiceImpl.class.getDeclaredMethod(
                "enrichMaterialItemWithReportInfo",
                MaterialItemVO.class
        );
        method.setAccessible(true);
        method.invoke(materialSearchService, materialItem);

        // 验证结果 - title 和 source 应该为 null
        assertNull(materialItem.getTitle());
        assertNull(materialItem.getSource());
    }
}
