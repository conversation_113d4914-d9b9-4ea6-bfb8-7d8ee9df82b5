package com.quantchi.knowledge.center.service;

import com.quantchi.knowledge.center.bean.system.vo.MaterialItemVO;
import com.quantchi.knowledge.center.helper.ElasticsearchHelper;
import com.quantchi.knowledge.center.service.impl.MaterialSearchServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * MaterialSearchService 研报信息补充功能测试
 */
public class MaterialSearchServiceEnrichmentTest {

    @Mock
    private ElasticsearchHelper elasticsearchHelper;

    private MaterialSearchServiceImpl materialSearchService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        materialSearchService = new MaterialSearchServiceImpl(null, null, null, elasticsearchHelper);
    }

    @Test
    void testEnrichMaterialItemsWithReportInfo() throws Exception {
        // 准备测试数据
        List<MaterialItemVO> materialItems = new ArrayList<>();
        MaterialItemVO materialItem1 = new MaterialItemVO();
        materialItem1.setDocId("test-doc-id-1");
        materialItem1.setChunkId(123L);
        materialItem1.setChunk("测试素材内容1");
        materialItems.add(materialItem1);

        MaterialItemVO materialItem2 = new MaterialItemVO();
        materialItem2.setDocId("test-doc-id-2");
        materialItem2.setChunkId(124L);
        materialItem2.setChunk("测试素材内容2");
        materialItems.add(materialItem2);

        List<String> docIds = Arrays.asList("test-doc-id-1", "test-doc-id-2");

        // 使用反射调用私有方法
        Method method = MaterialSearchServiceImpl.class.getDeclaredMethod(
                "enrichMaterialItemsWithReportInfo",
                List.class,
                List.class
        );
        method.setAccessible(true);
        method.invoke(materialSearchService, materialItems, docIds);

        // 由于我们没有mock ES查询，title和source应该为null
        // 这个测试主要验证方法不会抛出异常
        assertNull(materialItem1.getTitle());
        assertNull(materialItem1.getSource());
        assertNull(materialItem2.getTitle());
        assertNull(materialItem2.getSource());
    }

    @Test
    void testEnrichMaterialItemsWithEmptyDocIds() throws Exception {
        // 准备测试数据 - 空的docIds列表
        List<MaterialItemVO> materialItems = new ArrayList<>();
        MaterialItemVO materialItem = new MaterialItemVO();
        materialItem.setDocId("");
        materialItem.setChunkId(123L);
        materialItem.setChunk("测试素材内容");
        materialItems.add(materialItem);

        List<String> docIds = new ArrayList<>(); // 空列表

        // 使用反射调用私有方法
        Method method = MaterialSearchServiceImpl.class.getDeclaredMethod(
                "enrichMaterialItemsWithReportInfo",
                List.class,
                List.class
        );
        method.setAccessible(true);
        method.invoke(materialSearchService, materialItems, docIds);

        // 验证结果 - title 和 source 应该为 null
        assertNull(materialItem.getTitle());
        assertNull(materialItem.getSource());
    }
}
