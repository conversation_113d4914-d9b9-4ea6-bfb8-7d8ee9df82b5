package com.quantchi.knowledge.center.service;

import com.quantchi.knowledge.center.bean.vo.TocEntryVO;
import com.quantchi.knowledge.center.service.impl.MaterialSearchServiceImpl;
import org.junit.jupiter.api.Test;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

/**
 * 素材检索服务测试类
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
public class MaterialSearchServiceTest {

    @Test
    public void testConvertTocEntriesToMarkdown() throws Exception {
        // 创建测试数据
        List<TocEntryVO> tocEntries = new ArrayList<>();
        
        // 第一级标题
        TocEntryVO entry1 = new TocEntryVO();
        entry1.setTitle("第一章 概述");
        entry1.setLevel(1);
        entry1.setChildren(new ArrayList<>());
        
        // 第二级标题
        TocEntryVO entry1_1 = new TocEntryVO();
        entry1_1.setTitle("1.1 背景介绍");
        entry1_1.setLevel(2);
        entry1_1.setChildren(new ArrayList<>());
        
        TocEntryVO entry1_2 = new TocEntryVO();
        entry1_2.setTitle("1.2 研究目标");
        entry1_2.setLevel(2);
        entry1_2.setChildren(new ArrayList<>());
        
        entry1.getChildren().add(entry1_1);
        entry1.getChildren().add(entry1_2);
        
        // 第二个一级标题
        TocEntryVO entry2 = new TocEntryVO();
        entry2.setTitle("第二章 分析");
        entry2.setLevel(1);
        entry2.setChildren(new ArrayList<>());
        
        tocEntries.add(entry1);
        tocEntries.add(entry2);
        
        // 使用反射调用私有方法进行测试
        MaterialSearchServiceImpl service = new MaterialSearchServiceImpl(null, null, null, null);
        Method method = MaterialSearchServiceImpl.class.getDeclaredMethod(
            "convertTocEntriesToMarkdown", 
            List.class, 
            StringBuilder.class, 
            int.class
        );
        method.setAccessible(true);
        
        StringBuilder markdown = new StringBuilder();
        method.invoke(service, tocEntries, markdown, 1);
        
        String result = markdown.toString();
        System.out.println("转换结果：");
        System.out.println(result);
        
        // 验证结果
        assert result.contains("# 第一章 概述") : "应该包含一级标题";
        assert result.contains("## 1.1 背景介绍") : "应该包含二级标题";
        assert result.contains("## 1.2 研究目标") : "应该包含二级标题";
        assert result.contains("# 第二章 分析") : "应该包含第二个一级标题";
        
        System.out.println("测试通过！");
    }
}
