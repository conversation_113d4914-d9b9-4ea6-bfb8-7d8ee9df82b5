# MaterialSearch 接口增强说明

## 需求描述
针对 `materialSearch` 接口，需要增加一个逻辑：
- 现在通过 `materialItem.setDocId(item.getString("doc_id"))` 获取到的是 ES 的 report 库的 id
- 获取到 id 之后，需要从库里去取这个 report 研报库的标题和来源
- 查询的方法参考 `libraryInfoService.getService(EsIndexEnum.getEsIndexByType(type)).getLibraryList(query)`
- 对应的字段是 `title` 和 `source`，要加到 `List<MaterialItemVO>` 里面返回

## 实现方案

### 1. 修改 MaterialItemVO 类
在 `MaterialItemVO` 中添加了两个新字段：

```java
@ApiModelProperty("研报标题")
private String title;

@ApiModelProperty("研报来源")
private String source;
```

### 2. 修改 MaterialSearchServiceImpl 类

#### 2.1 添加依赖注入
```java
private final ElasticsearchHelper elasticsearchHelper;
```

#### 2.2 修改 materialSearch 方法
在原有的循环中添加了研报信息补充逻辑：

```java
for (int i = 0; i < dataArray.size(); i++) {
    final JSONObject item = dataArray.getJSONObject(i);
    final MaterialItemVO materialItem = new MaterialItemVO();
    materialItem.setChunkId(item.getLong("chunk_id"));
    materialItem.setDocId(item.getString("doc_id"));
    materialItem.setChunk(item.getString("chunk"));
    
    // 根据 doc_id 从 ES 研报库中获取 title 和 source
    enrichMaterialItemWithReportInfo(materialItem);
    
    materialItems.add(materialItem);
}
```

#### 2.3 新增 enrichMaterialItemWithReportInfo 方法
```java
/**
 * 根据 doc_id 从 ES 研报库中获取 title 和 source，并设置到 MaterialItemVO 中
 *
 * @param materialItem 素材项
 */
private void enrichMaterialItemWithReportInfo(final MaterialItemVO materialItem) {
    try {
        final String docId = materialItem.getDocId();
        if (CharSequenceUtil.isBlank(docId)) {
            return;
        }

        // 从 ES 研报库中查询 title 和 source 字段
        final String[] includes = {"title", "source"};
        final Map<String, Object> reportInfo = elasticsearchHelper.getDataById(
                EsIndexEnum.REPORT.getEsIndex(), 
                docId, 
                includes, 
                null
        );

        if (reportInfo != null && !reportInfo.isEmpty()) {
            final Object title = reportInfo.get("title");
            final Object source = reportInfo.get("source");
            
            if (title instanceof String) {
                materialItem.setTitle((String) title);
            }
            
            if (source instanceof String) {
                materialItem.setSource((String) source);
            }
        }

    } catch (final Exception e) {
        log.error("获取研报信息失败，docId: {}", materialItem.getDocId(), e);
        // 发生异常时不影响主流程，只记录日志
    }
}
```

## 技术实现细节

### 1. ES 查询方式
使用 `ElasticsearchHelper.getDataById()` 方法：
- 索引：`EsIndexEnum.REPORT.getEsIndex()` (对应 "icir_report")
- 文档ID：从素材检索API返回的 `doc_id`
- 查询字段：只查询 `title` 和 `source` 字段，提高查询效率

### 2. 异常处理
- 如果 `docId` 为空，直接返回，不进行查询
- 如果 ES 查询失败，只记录错误日志，不影响主流程
- 确保即使研报信息获取失败，素材检索的主要功能仍然正常

### 3. 性能考虑
- 只查询必要的字段（`title` 和 `source`）
- 使用 ES 的 `getDataById` 方法，效率较高
- 异常处理确保不会因为单个研报查询失败而影响整体性能

## 返回结果示例

修改后的 `MaterialItemVO` 返回结果：

```json
{
  "chunkId": 123,
  "docId": "report-doc-id-123",
  "chunk": "这是素材内容...",
  "title": "人工智能产业发展研究报告",
  "source": "某研究机构"
}
```

## 测试
已创建单元测试 `MaterialSearchServiceEnrichmentTest` 来验证功能：
- 测试正常情况下的研报信息补充
- 测试 docId 为空的情况
- 测试 ES 查询返回 null 的情况

## 注意事项
1. 确保 ES 中的研报索引 `icir_report` 包含 `title` 和 `source` 字段
2. 该功能是增量式的，不会影响现有的素材检索逻辑
3. 如果研报信息获取失败，`title` 和 `source` 字段将为 `null`
