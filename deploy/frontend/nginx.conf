user root;
worker_processes 4;

error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;


events {
    worker_connections 1024;
}


http {
    gzip on;
    gzip_comp_level 5;
    gzip_min_length 1k;
    gzip_buffers 4 16k;
    gzip_proxied any;
    gzip_vary on;
    gzip_types
    application/javascript
    application/x-javascript
    text/javascript
    text/css
    text/xml
    application/xhtml+xml
    application/xml
    application/atom+xml
    application/rdf+xml
    application/rss+xml
    application/geo+json
    application/json
    application/ld+json
    application/manifest+json
    application/x-web-app-manifest+json
    image/svg+xml
    text/x-cross-domain-policy;
    gzip_disable "MSIE [1-6]\.";
    include mime.types;
    default_type application/octet-stream;

    # Existing Server block listening on 8002
    server {
        listen 8002;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection '1;mode=block';
        error_page 403 =404 /404.html; # Consider if you need a root/error page for 404.html here

        location / {
            client_max_body_size 100M;
            proxy_pass http://localhost:7752/;
            proxy_set_header Host $host:$server_port;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /preview {
            proxy_pass http://************:8012;
            client_max_body_size 100M;
        }

        location = /preview/index {
            return 403; # 返回 403 Forbidden
        }

        location /api {
            # 调整超时时间的配置
            proxy_connect_timeout 120s;
            proxy_read_timeout 120s;
            proxy_send_timeout 120s;
            send_timeout 120s;

            client_max_body_size 100M;
            proxy_pass http://icir-backend-prod:8612;

            # 添加代理头信息
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Host $host;

            # 代理缓冲设置
            proxy_buffer_size 16k;
            proxy_buffers 4 64k;
            proxy_busy_buffers_size 128k;
            proxy_temp_file_write_size 128k;
        }

        location /api/qa/stream/chatWithThink {
            proxy_pass http://************:8612; # Make sure 'useProxyPass' is defined or replaced
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_read_timeout 1800;
            # 禁用 Nginx 的代理缓冲
            proxy_buffering off;
            # 启用分块传输编码 允许服务器分块发送数据，不需要预先知道内容长度
            chunked_transfer_encoding on;
            # 在发送数据时，尽可能将多个数据包组合成一个大的数据包 与 tcp_nodelay 搭配使用，Nginx 会自动在合适的时机启用合适的选项
            tcp_nopush on;
            # 禁用 Nagle 算法 允许小数据包立即发送，不等待累积
            tcp_nodelay on;
            # 禁用 gzip 压缩 避免压缩带来的延迟
            gzip off;
        }

        location /api/report/stream/chats {
            proxy_pass http://************:8612; # Make sure 'useProxyPass' is defined or replaced
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_read_timeout 1800;
            # 禁用 Nginx 的代理缓冲
            proxy_buffering off;
            # 启用分块传输编码 允许服务器分块发送数据，不需要预先知道内容长度
            chunked_transfer_encoding on;
            # 在发送数据时，尽可能将多个数据包组合成一个大的数据包 与 tcp_nodelay 搭配使用，Nginx 会自动在合适的时机启用合适的选项
            tcp_nopush on;
            # 禁用 Nagle 算法 允许小数据包立即发送，不等待累积
            tcp_nodelay on;
            # 禁用 gzip 压缩 避免压缩带来的延迟
            gzip off;
        }
    } # End of server block listening on 8002

    # Existing Server block listening on 7752
    server {
        listen 7752;
        server_name _;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection '1;mode=block';
        error_page 403 =404 /404.html;
        location / {
            root /opt/html;
            index index.html;
            try_files $uri $uri/ /index.html;
        }
    }

    # 移除了7755端口的H5前端服务器块

    server {
        listen 8080; # Dify将连接到这个HTTP端口

        location /api/v1/ {
            proxy_pass https://*************/api/v1/; # 目标DeepSeek API

            # 禁用上游SSL证书验证
            proxy_ssl_verify off;

            # 可能需要的其他代理设置
            proxy_set_header Host $host; # 或者目标服务器期望的Host头
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # 如果遇到 upstream sent no valid HTTP/1.0 header while reading response header from upstream
            # 可以尝试添加以下配置
            # proxy_http_version 1.1;
            # proxy_set_header Connection "";
        }
    }

    # Server block listening on 8003, forwarding to *************:8003
    server {
        listen 8003;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection '1;mode=block';
        error_page 403 =404 /404.html;

        location / {
            client_max_body_size 100M;
            proxy_pass http://*************:8003;
            proxy_set_header Host $host:$server_port;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 超时配置
            proxy_connect_timeout 120s;
            proxy_read_timeout 120s;
            proxy_send_timeout 120s;
            send_timeout 120s;
            
            # 代理缓冲设置
            proxy_buffer_size 16k;
            proxy_buffers 4 64k;
            proxy_busy_buffers_size 128k;
            proxy_temp_file_write_size 128k;
        }
    }
} # End of http block