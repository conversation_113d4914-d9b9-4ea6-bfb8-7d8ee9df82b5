#!/bin/bash
# 全自动前端构建脚本 - 从dist.zip构建并推送到阿里云镜像仓库

# 颜色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 配置信息
REGISTRY="crpi-dnhh3gahtc9ne4yy.cn-hangzhou.personal.cr.aliyuncs.com"
REGISTRY_NAMESPACE="fuzhou-icir"
IMAGE_NAME="fuzhou-frontend-prod"
IMAGE_TAG="latest"
FULL_IMAGE_NAME="${REGISTRY}/${REGISTRY_NAMESPACE}/${IMAGE_NAME}:${IMAGE_TAG}"
PROJECT_ROOT="$(cd "$(dirname "$0")/../.." && pwd)"
DEPLOY_DIR="${PROJECT_ROOT}/deploy/frontend"
FRONTEND_DIR="${PROJECT_ROOT}/deploy/frontend"
DIST_DIR="${DEPLOY_DIR}/dist"

# 打印带颜色的日志
log_info() {
  echo -e "${GREEN}[INFO] $1${NC}"
}

log_warn() {
  echo -e "${YELLOW}[WARN] $1${NC}"
}

log_error() {
  echo -e "${RED}[ERROR] $1${NC}"
}

# 显示头部信息
log_info "========================================"
log_info "      前端自动构建部署脚本(镜像仓库版)"
log_info "========================================"
log_info "项目根目录: ${PROJECT_ROOT}"
log_info "部署目录: ${DEPLOY_DIR}"
log_info "前端代码目录: ${FRONTEND_DIR}"
log_info "镜像名称: ${FULL_IMAGE_NAME}"

# 1. 直接使用当前目录下的dist.zip打包文件
log_info "检查当前目录下的前端打包文件..."

# 检查当前目录下的dist.zip文件
if [ -f "${FRONTEND_DIR}/dist.zip" ]; then
  DIST_ZIP="${FRONTEND_DIR}/dist.zip"
  log_info "找到前端打包文件: ${DIST_ZIP}"
  USE_PREBUILT=true
else
  log_error "当前目录下找不到前端打包文件！"
  log_info "请将dist.zip放到 ${FRONTEND_DIR} 目录下"
  exit 1
fi

# 2. 解压前端打包文件
log_info "使用打包文件: ${DIST_ZIP}"

# 清理目标目录
log_info "清理目标目录..."
rm -rf "${DIST_DIR}"
mkdir -p "${DIST_DIR}"

# 解压zip文件
log_info "解压前端打包文件..."
unzip -q "${DIST_ZIP}" -d "${DEPLOY_DIR}/dist_tmp"

# 检查解压后的目录结构
DIST_TMP_DIR="${DEPLOY_DIR}/dist_tmp"
if [ -f "${DIST_TMP_DIR}/index.html" ]; then
  # 直接解压出了所需的文件
  log_info "直接将解压文件移动到dist目录"
  cp -r "${DIST_TMP_DIR}"/* "${DIST_DIR}/"
else
  # 尝试查找含有index.html的目录
  INDEX_DIR=$(find "${DIST_TMP_DIR}" -name "index.html" | head -1 | xargs dirname)
  if [ -n "${INDEX_DIR}" ]; then
    log_info "找到包含index.html的目录: ${INDEX_DIR}，移动到dist"
    cp -r "${INDEX_DIR}"/* "${DIST_DIR}/"
  elif [ -d "${DIST_TMP_DIR}/dist" ]; then
    # 查看是否有dist目录
    log_info "找到dist目录，移动到目标目录"
    cp -r "${DIST_TMP_DIR}/dist"/* "${DIST_DIR}/"
  else
    # 如果都没找到，尝试找最上层的子目录
    SUBDIR=$(find "${DIST_TMP_DIR}" -mindepth 1 -maxdepth 1 -type d | head -1)
    if [ -n "${SUBDIR}" ]; then
      log_info "使用子目录: ${SUBDIR}"
      cp -r "${SUBDIR}"/* "${DIST_DIR}/"
    else
      log_error "无法找到解压后的前端文件！"
      exit 1
    fi
  fi
fi

# 清理临时目录
rm -rf "${DIST_TMP_DIR}"

# 3. 构建docker镜像
log_info "构建Docker镜像..."
cd "${DEPLOY_DIR}"

# 确认dist目录存在且非空
if [ ! -d "${DIST_DIR}" ] || [ -z "$(ls -A "${DIST_DIR}")" ]; then
  log_error "dist目录不存在或为空！"
  exit 1
fi

docker build --no-cache -t ${FULL_IMAGE_NAME} -f Dockerfile_prod .
if [ $? -ne 0 ]; then
  log_error "构建Docker镜像失败!"
  exit 1
fi
log_info "Docker镜像构建成功"

# 4. 登录阿里云镜像仓库
log_info "登录阿里云镜像仓库..."
docker login --username=zhangzp07 -p="7VUuF@gYe@uwUWXA" ${REGISTRY}
if [ $? -ne 0 ]; then
  log_error "登录阿里云镜像仓库失败!"
  exit 1
fi
log_info "阿里云镜像仓库登录成功"

# 5. 推送镜像到阿里云镜像仓库
log_info "推送镜像到阿里云镜像仓库..."
docker push ${FULL_IMAGE_NAME}
if [ $? -ne 0 ]; then
  log_error "推送镜像失败!"
  exit 1
fi
log_info "镜像推送成功"

# 显示完成信息
log_info "========================================"
log_info "构建过程全部完成!"
log_info "镜像名称: ${FULL_IMAGE_NAME}"
log_info "========================================"
log_info "镜像已推送到阿里云仓库，可以使用 deploy-harbor.sh 脚本在服务器上部署"
