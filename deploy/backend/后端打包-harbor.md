# ICIR 后端打包部署指南（阿里云镜像仓库版）

本文档介绍如何使用阿里云镜像仓库进行 ICIR 后端服务的打包和部署。

## 一、前提条件

1. 开发机器和服务器均已安装 Docker
2. 开发机器已安装 Maven
3. 服务器可以连接到阿里云镜像仓库
4. 已有阿里云镜像仓库的访问凭证

## 二、构建并推送镜像

在开发机器上执行以下步骤：

1. 进入项目根目录
2. 运行构建脚本

```bash
cd /path/to/icir
chmod +x deploy/backend/build-harbor.sh
./deploy/backend/build-harbor.sh
```

该脚本会执行以下操作：
- 使用 Maven 编译项目生成 JAR 包
- 构建 Docker 镜像
- 登录阿里云镜像仓库
- 将镜像推送到阿里云镜像仓库

构建完成后，镜像将被推送到以下地址：
```
crpi-dnhh3gahtc9ne4yy.cn-hangzhou.personal.cr.aliyuncs.com/fuzhou-icir/icir-backend-prod:latest
```

## 三、在服务器上部署

将 `deploy-harbor.sh` 脚本上传到服务器，然后执行：

```bash
# 设置脚本可执行权限
chmod +x deploy-harbor.sh

# 执行部署脚本
./deploy-harbor.sh
```

该脚本会执行以下操作：
- 登录阿里云镜像仓库
- 拉取最新的 Docker 镜像
- 停止并删除旧容器（如果存在）
- 清理无标签的旧镜像
- 启动新容器
- 显示容器日志

## 四、镜像和容器信息

- 镜像名称：`crpi-dnhh3gahtc9ne4yy.cn-hangzhou.personal.cr.aliyuncs.com/fuzhou-icir/icir-backend-prod:latest`
- 容器名称：`icir-backend-prod`
- 端口映射：`8612:8612`

## 五、登录凭证信息

阿里云镜像仓库信息：
- 仓库地址：`crpi-dnhh3gahtc9ne4yy.cn-hangzhou.personal.cr.aliyuncs.com`
- 用户名：`zhangzp07`
- 密码：`7VUuF@gYe@uwUWXA`
- 命名空间：`fuzhou-icir`

## 六、常见问题

### 1. 镜像拉取失败

如果镜像拉取失败，可能是网络问题或登录凭证过期。请重新尝试登录阿里云镜像仓库：

```bash
docker login --username=zhangzp07 -p="7VUuF@gYe@uwUW" crpi-dnhh3gahtc9ne4yy.cn-hangzhou.personal.cr.aliyuncs.com
```

### 2. 容器启动失败

检查容器启动日志：

```bash
docker logs icir-backend-prod
```

### 3. 镜像过大或构建缓慢

建议定期清理无用的镜像和容器：

```bash
# 删除无标签的镜像
docker images | grep "<none>" | awk '{print $3}' | xargs -r docker rmi

# 删除停止的容器
docker container prune
```
