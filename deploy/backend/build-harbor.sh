#!/bin/bash
# 全自动构建脚本 - 从源代码构建并推送到阿里云镜像仓库

# 颜色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 配置信息
REGISTRY="crpi-dnhh3gahtc9ne4yy.cn-hangzhou.personal.cr.aliyuncs.com"
REGISTRY_NAMESPACE="fuzhou-icir"
IMAGE_NAME="icir-backend-prod"
IMAGE_TAG="latest"
FULL_IMAGE_NAME="${REGISTRY}/${REGISTRY_NAMESPACE}/${IMAGE_NAME}:${IMAGE_TAG}"
PROJECT_ROOT="$(cd "$(dirname "$0")/../.." && pwd)"
DEPLOY_DIR="${PROJECT_ROOT}/deploy/backend"
TARGET_DIR="${PROJECT_ROOT}/target"
JAR_NAME="icir.jar"

# 打印带颜色的日志
log_info() {
  echo -e "${GREEN}[INFO] $1${NC}"
}

log_warn() {
  echo -e "${YELLOW}[WARN] $1${NC}"
}

log_error() {
  echo -e "${RED}[ERROR] $1${NC}"
}

# 显示头部信息
log_info "========================================"
log_info "    ICIR 后端自动构建部署脚本(镜像仓库版)"
log_info "========================================"
log_info "项目根目录: ${PROJECT_ROOT}"
log_info "部署目录: ${DEPLOY_DIR}"
log_info "目标JAR: ${TARGET_DIR}/${JAR_NAME}"
log_info "镜像名称: ${FULL_IMAGE_NAME}"

# 1. 使用Maven构建项目
log_info "开始构建项目..."
cd "${PROJECT_ROOT}"

# 检查是否有 mvnw（Maven Wrapper）
if [ -f "./mvnw" ]; then
  ./mvnw clean package -DskipTests
else
  mvn clean package -DskipTests
fi

# 检查构建结果
if [ ! -f "${TARGET_DIR}/${JAR_NAME}" ]; then
  # 检查是否是其他名称的JAR
  JAR_FILES=$(find "${TARGET_DIR}" -name "*.jar" ! -name "*sources.jar" ! -name "*javadoc.jar" -type f)
  JAR_COUNT=$(echo "${JAR_FILES}" | grep -v "^$" | wc -l)
  
  if [ "${JAR_COUNT}" -eq "0" ]; then
    log_error "构建失败，没有找到JAR文件!"
    exit 1
  elif [ "${JAR_COUNT}" -eq "1" ]; then
    # 只有一个JAR文件，使用它
    JAR_PATH="${JAR_FILES}"
    JAR_NAME=$(basename "${JAR_PATH}")
    log_warn "使用找到的JAR文件: ${JAR_NAME}"
  else
    # 有多个JAR文件，使用最新的一个
    JAR_PATH=$(ls -t ${JAR_FILES} | head -1)
    JAR_NAME=$(basename "${JAR_PATH}")
    log_warn "找到多个JAR文件，使用最新的: ${JAR_NAME}"
  fi
fi

log_info "项目构建成功"

# 2. 复制JAR文件到部署目录
log_info "复制JAR文件到部署目录..."
cp "${TARGET_DIR}/${JAR_NAME}" "${DEPLOY_DIR}/"
if [ $? -ne 0 ]; then
  log_error "复制JAR文件失败!"
  exit 1
fi
log_info "JAR文件已复制到部署目录"

# 3. 构建Docker镜像
log_info "构建Docker镜像..."
cd "${DEPLOY_DIR}"
docker buildx build --platform linux/amd64 --no-cache -t ${FULL_IMAGE_NAME} .
if [ $? -ne 0 ]; then
  log_error "构建Docker镜像失败!"
  exit 1
fi
log_info "Docker镜像构建成功"

# 4. 登录阿里云镜像仓库
log_info "登录阿里云镜像仓库..."
docker login --username=zhangzp07 -p="7VUuF@gYe@uwUWXA" ${REGISTRY}
if [ $? -ne 0 ]; then
  log_error "登录阿里云镜像仓库失败!"
  exit 1
fi
log_info "阿里云镜像仓库登录成功"

# 5. 推送镜像到阿里云镜像仓库
log_info "推送镜像到阿里云镜像仓库..."
docker push ${FULL_IMAGE_NAME}
if [ $? -ne 0 ]; then
  log_error "推送镜像失败!"
  exit 1
fi
log_info "镜像推送成功"

# 6. 清理临时文件
log_info "清理临时文件..."
rm -f "${DEPLOY_DIR}/${JAR_NAME}"
log_info "临时文件已清理"

# 显示完成信息
log_info "========================================"
log_info "构建过程全部完成!"
log_info "镜像名称: ${FULL_IMAGE_NAME}"
log_info "========================================"
log_info "镜像已推送到阿里云仓库，可以使用 deploy-harbor.sh 脚本在服务器上部署"
