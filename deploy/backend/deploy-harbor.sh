#!/bin/bash
# 一键部署脚本 - 通过阿里云镜像仓库拉取镜像并部署

# 颜色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 配置信息
REGISTRY="crpi-dnhh3gahtc9ne4yy.cn-hangzhou.personal.cr.aliyuncs.com"
REGISTRY_NAMESPACE="fuzhou-icir"
IMAGE_NAME="icir-backend-prod"
IMAGE_TAG="latest"
FULL_IMAGE_NAME="${REGISTRY}/${REGISTRY_NAMESPACE}/${IMAGE_NAME}:${IMAGE_TAG}"
CONTAINER_NAME="icir-backend-prod"
PORT="8612:8612"
DATA_DIR="/data/backend"

# 打印带颜色的日志
log_info() {
  echo -e "${GREEN}[INFO] $1${NC}"
}

log_warn() {
  echo -e "${YELLOW}[WARN] $1${NC}"
}

log_error() {
  echo -e "${RED}[ERROR] $1${NC}"
}

# 显示头部信息
log_info "========================================"
log_info "    ICIR 后端镜像部署脚本(镜像仓库版)"
log_info "========================================"
log_info "镜像名称: ${FULL_IMAGE_NAME}"
log_info "容器名称: ${CONTAINER_NAME}"
log_info "端口映射: ${PORT}"

# 1. 登录阿里云镜像仓库
log_info "登录阿里云镜像仓库..."
docker login --username=zhangzp07 -p="7VUuF@gYe@uwUWXA" ${REGISTRY}
if [ $? -ne 0 ]; then
  log_error "登录阿里云镜像仓库失败!"
  exit 1
fi
log_info "阿里云镜像仓库登录成功"

# 2. 拉取最新镜像
log_info "拉取最新镜像..."
docker pull ${FULL_IMAGE_NAME}
if [ $? -ne 0 ]; then
  log_error "拉取镜像失败!"
  exit 1
fi
log_info "镜像拉取成功"

# 3. 停止并删除旧容器（如果存在）
if [ "$(docker ps -a -q -f name=${CONTAINER_NAME})" ]; then
  log_info "停止并删除旧容器..."
  docker stop ${CONTAINER_NAME}
  docker rm ${CONTAINER_NAME}
  log_info "旧容器已删除"
fi

# 4. 删除无标签的旧镜像（可选，避免占用太多磁盘空间）
log_info "清理无标签的旧镜像..."
docker images | grep "<none>" | awk '{print $3}' | xargs -r docker rmi
log_info "无标签的旧镜像已清理"

# 5. 启动新容器
log_info "启动新容器..."
docker run -d --name ${CONTAINER_NAME} \
  -p ${PORT} \
  --restart=always \
  ${FULL_IMAGE_NAME}

if [ $? -ne 0 ]; then
  log_error "启动容器失败!"
  exit 1
fi

log_info "容器启动成功，容器ID: $(docker ps -q -f name=${CONTAINER_NAME})"

# 6. 显示容器日志
log_info "显示容器日志（按 Ctrl+C 退出日志查看）:"
docker logs -f ${CONTAINER_NAME}
