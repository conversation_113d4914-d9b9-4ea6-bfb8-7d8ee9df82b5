# AI报告功能API使用示例

## 1. 生成报告大纲

### 请求示例
```bash
curl -X POST "http://localhost:8080/sys/report/ai/generateOutline" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "title": "人工智能产业发展报告",
    "description": "分析当前人工智能产业的发展现状、趋势和挑战，为相关决策提供参考",
    "theme": "产业类",
    "templateFileId": 123
  }'
```

### 响应示例
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "title": "新一代人工智能产业研究报告",
      "level": 1,
      "anchorId": "新一代人工智能产业研究报告",
      "children": []
    },
    {
      "title": "一、产业链图谱",
      "level": 2,
      "anchorId": "一-产业链图谱",
      "children": [
        {
          "title": "（一）产业定义与范畴",
          "level": 3,
          "anchorId": "一-产业定义与范畴",
          "children": [
            {
              "title": "1. 人工智能产业核心界定",
              "level": 4,
              "anchorId": "1-人工智能产业核心界定",
              "children": []
            },
            {
              "title": "2. 产业细分领域划分（基础层/技术层/应用层）",
              "level": 4,
              "anchorId": "2-产业细分领域划分基础层技术层应用层",
              "children": []
            }
          ]
        },
        {
          "title": "（二）全球产业链布局",
          "level": 3,
          "anchorId": "二-全球产业链布局",
          "children": [
            {
              "title": "1. 主要国家产业分工格局",
              "level": 4,
              "anchorId": "1-主要国家产业分工格局",
              "children": []
            }
          ]
        }
      ]
    }
  ]
}
```

## 2. 生成AI报告

### 请求示例
```bash
curl -X POST "http://localhost:8080/sys/report/ai/generate" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "title": "人工智能产业发展报告",
    "description": "分析当前人工智能产业的发展现状、趋势和挑战，为相关决策提供参考",
    "theme": "产业类",
    "templateFileId": 123,
    "outline": [
      {
        "title": "人工智能产业发展报告",
        "level": 1,
        "anchorId": "人工智能产业发展报告"
      },
      {
        "title": "第一章 概述",
        "level": 2,
        "anchorId": "第一章-概述"
      },
      {
        "title": "1.1 背景介绍",
        "level": 3,
        "anchorId": "1-1-背景介绍"
      }
    ]
  }'
```

### 响应示例
```json
{
  "code": 200,
  "message": "操作成功",
  "data": "# 人工智能产业发展报告\n\n## 第一章 概述\n\n### 1.1 背景介绍\n\n人工智能作为新一轮科技革命和产业变革的重要驱动力..."
}
```

## 3. 获取AI报告详情

### 请求示例
```bash
curl -X GET "http://localhost:8080/sys/report/ai/detail/123" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 响应示例
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "reportId": 123,
    "title": "人工智能产业发展报告",
    "description": "分析当前人工智能产业的发展现状、趋势和挑战，为相关决策提供参考",
    "theme": "产业类",
    "templateFileId": 456,
    "templateFileName": "AI产业报告模板.docx",
    "outline": "{\"outline\":[{\"title\":\"第一章 概述\",\"children\":[{\"title\":\"1.1 背景介绍\"},{\"title\":\"1.2 研究目的\"}]}]}",
    "content": "# 人工智能产业发展报告\n\n## 第一章 概述\n\n### 1.1 背景介绍\n\n人工智能作为新一轮科技革命和产业变革的重要驱动力...",
    "outlineStatus": 1,
    "contentStatus": 2,
    "displayStatus": 0,
    "createTime": "2024-12-25 10:00:00",
    "updateTime": "2024-12-25 11:30:00"
  }
}
```

## 4. 获取主题选项

### 请求示例
```bash
curl -X GET "http://localhost:8080/sys/report/ai/themes" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 响应示例
```json
{
  "code": 200,
  "message": "操作成功",
  "data": ["产业类", "技术类", "企业类", "招商类", "其他"]
}
```

## 5. 我的报告列表（支持类型过滤）

### 请求示例 - 查询所有报告
```bash
curl -X POST "http://localhost:8080/sys/report/myReportList" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "pageNum": 1,
    "pageSize": 10,
    "keyword": "人工智能"
  }'
```

### 请求示例 - 只查询AI报告
```bash
curl -X POST "http://localhost:8080/sys/report/myReportList" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "pageNum": 1,
    "pageSize": 10,
    "type": 2
  }'
```

### 响应示例
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "total": 15,
    "list": [
      {
        "id": 123,
        "title": "人工智能产业发展报告",
        "author": null,
        "issuingAgency": null,
        "introduce": null,
        "summarize": "本报告分析了人工智能产业的发展现状...",
        "industryTags": [],
        "publishDate": "2024-12-25",
        "fileId": null,
        "downloadUrl": null,
        "previewUrl": null,
        "homePageImageUrl": null,
        "clickTimes": 0,
        "downloadTimes": 0,
        "displayStatus": 0,
        "auditStatus": 1,
        "type": 2,
        "lastEditedAgo": "2小时前"
      }
    ],
    "pageNum": 1,
    "pageSize": 10,
    "pages": 2
  }
}
```

## 状态说明

### 大纲状态（outlineStatus）
- 0：未生成
- 1：已生成  
- 2：生成失败

### 内容状态（contentStatus）
- 0：未生成
- 1：生成中
- 2：已生成
- 3：生成失败

### 报告类型（type）
- 1：普通报告
- 2：AI报告

### 展示状态（displayStatus）
- 0：未公开
- 1：已公开
- 2：已下架

## 使用流程示例

1. **填写信息**：用户填写报告标题、描述、主题等基础信息
2. **生成大纲**：调用生成大纲接口，获得AI生成的报告大纲
3. **编辑大纲**：前端允许用户编辑大纲结构（增删改章节）
4. **生成报告**：基于最终大纲调用生成报告接口，创建报告并生成内容
5. **查看详情**：获取完整的报告信息
6. **发布报告**：使用现有的公开报告接口发布

## 错误处理

所有接口都会返回统一的错误格式：

```json
{
  "code": 500,
  "message": "具体错误信息",
  "data": null
}
```

常见错误：
- 401：未登录或token无效
- 400：参数错误
- 500：服务器内部错误（如AI接口调用失败）
