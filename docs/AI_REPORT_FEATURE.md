# AI报告生成功能

## 功能概述

AI报告生成功能允许用户通过填写基本信息，利用AI技术一键生成专业报告。该功能支持多种报告主题，可以上传参考模板，直接调用外部接口生成完整报告内容。

## 功能特性

1. **多主题支持**：产业类、技术类、企业类、招商类、其他
2. **模板参考**：支持上传PDF/DOCX格式的参考模板
3. **一键生成**：直接调用外部接口生成完整报告内容
4. **状态管理**：完整的生成状态跟踪
5. **列表兼容**：与现有报告列表完全兼容

## 数据库设计

### 新增表：sys_ai_report

```sql
CREATE TABLE `sys_ai_report` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `report_id` bigint(20) NOT NULL COMMENT '关联的报告ID',
  `description` varchar(500) NOT NULL COMMENT '报告描述',
  `theme` varchar(50) NOT NULL COMMENT '报告主题',
  `template_file_id` bigint(20) DEFAULT NULL COMMENT '参考模板文件ID',
  `template_file_name` varchar(255) DEFAULT NULL COMMENT '参考模板文件名',
  `outline` longtext COMMENT '生成的大纲内容(JSON格式)',
  `content` longtext COMMENT '最终生成的报告内容',
  `outline_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '大纲生成状态',
  `content_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '报告生成状态',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_valid` tinyint(1) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `idx_report_id` (`report_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI报告表';
```

### 修改表：sys_report

```sql
ALTER TABLE `sys_report` 
ADD COLUMN `type` tinyint(4) DEFAULT '1' COMMENT '报告种类，1普通报告/2AI报告';
```

## API接口

### 1. 生成报告大纲
- **接口**：`POST /sys/report/ai/generateOutline`
- **参数**：
  ```json
  {
    "title": "报告标题",
    "description": "报告描述",
    "theme": "产业类",
    "templateFileId": 123
  }
  ```
- **返回**：生成的大纲内容（TocEntryVO列表）
- **说明**：调用外部接口生成大纲，转换为结构化数据

### 2. 生成AI报告
- **接口**：`POST /sys/report/ai/generate`
- **参数**：
  ```json
  {
    "title": "报告标题",
    "description": "报告描述",
    "theme": "产业类",
    "templateFileId": 123,
    "outline": [
      {
        "title": "第一章 概述",
        "level": 1,
        "anchorId": "第一章-概述"
      }
    ]
  }
  ```
- **返回**：生成的报告内容（String类型）
- **说明**：基于用户编辑后的大纲创建报告并生成内容

### 3. 获取AI报告详情
- **接口**：`GET /sys/report/ai/detail/{reportId}`
- **返回**：完整的AI报告信息

### 4. 获取主题选项
- **接口**：`GET /sys/report/ai/themes`
- **返回**：可选的报告主题列表

### 5. 我的报告列表（兼容）
- **接口**：`POST /sys/report/myReportList`
- **参数**：
  ```json
  {
    "keyword": "搜索关键词（可选）",
    "type": 2,  // 1-普通报告，2-AI报告，不传则查询所有
    "pageNum": 1,
    "pageSize": 10
  }
  ```

## 状态说明

### 大纲状态（outline_status）
- 0：未生成
- 1：已生成
- 2：生成失败

### 内容状态（content_status）
- 0：未生成
- 1：生成中
- 2：已生成
- 3：生成失败

### 报告类型（type）
- 1：普通报告（上传的本地报告）
- 2：AI报告（在线生成的报告）

## 使用流程

1. **填写信息**：用户填写报告标题、描述、主题，可选择上传参考模板
2. **生成大纲**：点击"生成大纲"按钮，系统调用外部接口生成报告大纲
3. **编辑大纲**：用户可以在前端编辑大纲结构（增删改章节标题）
4. **生成报告**：点击"生成报告"按钮，系统基于最终大纲创建报告并生成内容
5. **查看报告**：在报告列表中查看和管理AI报告

## 技术实现

- **AI服务**：使用智谱AI（GLM-4-flash-250414）模型
- **同步处理**：报告内容生成采用同步方式，直接返回生成结果
- **状态管理**：完整的状态跟踪，记录生成成功或失败状态
- **文件解析**：支持解析上传的模板文件内容作为参考
- **数据兼容**：与现有报告系统完全兼容

## 注意事项

1. 模板文件解析功能需要进一步完善
2. AI接口调用需要配置正确的API密钥
3. 大纲和内容生成可能需要较长时间，建议使用异步处理
4. 建议对AI生成的内容进行人工审核
5. 需要合理设置AI接口的超时时间和重试机制
